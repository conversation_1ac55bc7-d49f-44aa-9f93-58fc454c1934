package com.example.monitordata1;

import android.database.ContentObserver;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {

    // 保存观察者实例
    private MyObserver myObserver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 修正URI路径（移除空格）
        Uri uri = Uri.parse("content://com.example.contentobserverdb/info");

        // 使用主线程的Handler
        Handler handler = new Handler(Looper.getMainLooper());

        // 创建并保存观察者实例
        myObserver = new MyObserver(handler);

        // 注册内容观察者
        getContentResolver().registerContentObserver(uri, true, myObserver);

        Log.i("MonitorData", "内容观察者已注册");
    }

    // 自定义内容观察者
    private class MyObserver extends ContentObserver {
        public MyObserver(Handler handler) {
            super(handler);
        }

        @Override
        public void onChange(boolean selfChange) {
            Log.i("监测数据变化", "有人动了你的数据库！");
            super.onChange(selfChange);
        }

        @Override
        public void onChange(boolean selfChange, Uri uri) {
            Log.i("监测数据变化", "数据变化URI: " + uri.toString());
            super.onChange(selfChange, uri);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 注销观察者
        if (myObserver != null) {
            getContentResolver().unregisterContentObserver(myObserver);
            Log.i("MonitorData", "内容观察者已注销");
        }
    }
}