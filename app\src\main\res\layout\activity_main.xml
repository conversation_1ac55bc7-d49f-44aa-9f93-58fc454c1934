<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="#F0F0F0"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="数据库监控运行中"
        android:textSize="24sp"
        android:textColor="#006600"
        android:layout_marginBottom="20dp"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="监控目标: content://com.example.contentobserverdb/info"
        android:textSize="16sp"
        android:textColor="#333333"
        android:layout_marginBottom="10dp"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="请查看Logcat中的'监测数据变化'日志"
        android:textSize="14sp"
        android:textColor="#666666"/>
</LinearLayout>