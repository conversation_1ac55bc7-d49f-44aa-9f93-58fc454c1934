1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.monitordata1"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="35"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b9fbc4cf0bb784a5ce9d4a3a3d8c6c\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.monitordata1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b9fbc4cf0bb784a5ce9d4a3a3d8c6c\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b9fbc4cf0bb784a5ce9d4a3a3d8c6c\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.monitordata1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b9fbc4cf0bb784a5ce9d4a3a3d8c6c\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b9fbc4cf0bb784a5ce9d4a3a3d8c6c\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:5:5-30:19
18        android:allowBackup="true"
18-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b9fbc4cf0bb784a5ce9d4a3a3d8c6c\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.MonitorData" >
29-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:13:9-49
30        <activity
30-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:15:9-23:20
31            android:name="com.example.monitordata1.MainActivity"
31-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:16:13-41
32            android:exported="true" >
32-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:17:13-36
33            <intent-filter>
33-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:18:13-22:29
34                <action android:name="android.intent.action.MAIN" />
34-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:19:17-69
34-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:19:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:21:17-77
36-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:21:27-74
37            </intent-filter>
38        </activity>
39
40        <!-- ContentProvider声明 -->
41        <provider
41-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:26:9-29:40
42            android:name="com.example.monitordata1.DataContentProvider"
42-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:27:13-48
43            android:authorities="com.example.contentobserverdb"
43-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:28:13-64
44            android:exported="false" />
44-->D:\16\MonitorData1\app\src\main\AndroidManifest.xml:29:13-37
45        <provider
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\349c9bae01799067ad36c2e6a19f1ac2\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
46            android:name="androidx.startup.InitializationProvider"
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\349c9bae01799067ad36c2e6a19f1ac2\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
47            android:authorities="com.example.monitordata1.androidx-startup"
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\349c9bae01799067ad36c2e6a19f1ac2\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
48            android:exported="false" >
48-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\349c9bae01799067ad36c2e6a19f1ac2\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
49            <meta-data
49-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\349c9bae01799067ad36c2e6a19f1ac2\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.emoji2.text.EmojiCompatInitializer"
50-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\349c9bae01799067ad36c2e6a19f1ac2\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
51                android:value="androidx.startup" />
51-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\349c9bae01799067ad36c2e6a19f1ac2\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d57fa36589e4e56a6f9aaf8bf0561d59\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
53-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d57fa36589e4e56a6f9aaf8bf0561d59\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
54                android:value="androidx.startup" />
54-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d57fa36589e4e56a6f9aaf8bf0561d59\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
56-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
57                android:value="androidx.startup" />
57-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
58        </provider>
59
60        <receiver
60-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
61            android:name="androidx.profileinstaller.ProfileInstallReceiver"
61-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
62            android:directBootAware="false"
62-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
63            android:enabled="true"
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
64            android:exported="true"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
65            android:permission="android.permission.DUMP" >
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
66            <intent-filter>
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
67                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
68            </intent-filter>
69            <intent-filter>
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
70                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
71            </intent-filter>
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
73                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
76                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39496f2141509c3d741a572476e45acf\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
77            </intent-filter>
78        </receiver>
79    </application>
80
81</manifest>
