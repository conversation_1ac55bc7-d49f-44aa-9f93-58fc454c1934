package com.example.monitordata1;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.net.Uri;
import android.util.Log;

/**
 * 简单的ContentProvider实现，用于支持ContentObserver监控
 */
public class DataContentProvider extends ContentProvider {

    private static final String TAG = "DataContentProvider"; // 日志标签
    public static final String AUTHORITY = "com.example.contentobserverdb"; // 权限标识
    public static final Uri CONTENT_URI = Uri.parse("content://" + AUTHORITY + "/info"); // 内容URI

    @Override
    public boolean onCreate() {
        Log.i(TAG, "ContentProvider已创建");
        return true; // 初始化成功
    }

    @Override
    public Cursor query(Uri uri, String[] projection, String selection, String[] selectionArgs, String sortOrder) {
        Log.i(TAG, "查询请求: " + uri.toString());
        
        // 返回简单的测试数据
        MatrixCursor cursor = new MatrixCursor(new String[]{"id", "name", "value"});
        cursor.addRow(new Object[]{1, "测试数据", "示例值"});
        return cursor;
    }

    @Override
    public String getType(Uri uri) {
        return "vnd.android.cursor.dir/vnd.example.info"; // MIME类型
    }

    @Override
    public Uri insert(Uri uri, ContentValues values) {
        Log.i(TAG, "插入数据: " + uri.toString());
        
        // 通知数据变化
        if (getContext() != null) {
            getContext().getContentResolver().notifyChange(uri, null);
        }
        
        return Uri.withAppendedPath(uri, "1"); // 返回新插入数据的URI
    }

    @Override
    public int delete(Uri uri, String selection, String[] selectionArgs) {
        Log.i(TAG, "删除数据: " + uri.toString());
        
        // 通知数据变化
        if (getContext() != null) {
            getContext().getContentResolver().notifyChange(uri, null);
        }
        
        return 1; // 返回删除的行数
    }

    @Override
    public int update(Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        Log.i(TAG, "更新数据: " + uri.toString());
        
        // 通知数据变化
        if (getContext() != null) {
            getContext().getContentResolver().notifyChange(uri, null);
        }
        
        return 1; // 返回更新的行数
    }
}
